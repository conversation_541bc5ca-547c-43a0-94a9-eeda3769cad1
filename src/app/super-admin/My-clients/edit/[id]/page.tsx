"use client";

import React, { useEffect, useState } from "react";
import { FaChevronDown, FaCheck } from "react-icons/fa";
import Image from "next/image";
import { backendApiClient, sendMultipart } from "@/utils/apiClient";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { queryClient } from "@/hooks/useGlobalContext";
import { useParams } from "next/navigation";
import { ClientType } from "@/utils/types";

const permissionData = [
  {
    title: "User & Role Management",
    permissions: [
      {
        id: "e517e8ec-795a-4471-b729-18b179b545f4",
        label: "Create, edit, delete, Dispatchers, Drivers, Users",
      },
      {
        id: "b28e7232-c1c5-49a7-8ab0-48218a594faf",
        label: "Assign Admins to taxi companies",
      },
      {
        id: "871c5291-f907-496f-b55a-29fb15f5e269",
        label: "Manage permissions for all roles",
      },
      {
        id: "de07c45c-188c-4c55-9c48-d07e5daab8ba",
        label: "Suspend/reactivate any user",
      },
    ],
  },
  {
    title: "Booking & Dispatch Control",
    permissions: [
      {
        id: "325e82e4-9936-4160-982a-dac40b3ee5dd",
        label: "View, edit, and manage all bookings",
      },
      {
        id: "8cbb59d3-eb1a-484e-b85a-3ff18dd46ff6",
        label: "Assign/reassign drivers to rides",
      },
      {
        id: "f4a8109a-78fc-4410-84ad-c9917592ae41",
        label: "View driver live locations",
      },
      {
        id: "1f86c3d5-998b-4c55-893c-031d52a3142a",
        label: "Cancel or reschedule any ride",
      },
    ],
  },
  {
    title: "Company & Financial Management",
    permissions: [
      {
        id: "411ff751-daaa-4059-8507-78a61a12e710",
        label: "Add, edit, or remove taxi companies",
      },
      {
        id: "175f509f-3d97-4f81-9b30-fb726ea1b9ed",
        label: "Manage company subscription plans",
      },
      {
        id: "ad0f3df5-353a-4ea6-890e-6db504236b10",
        label: "View & modify company-specific pricing and fare structures",
      },
      {
        id: "05943395-8021-4021-815d-aeb996ec5f11",
        label: "Access & edit company billing information",
      },
    ],
  },
  {
    title: "System & Policy Settings",
    permissions: [
      {
        id: "edeab525-e49d-482d-bd5a-0b2589c08a0f",
        label: "Define platform-wide fare policies",
      },
      {
        id: "a4161a2d-b100-41bb-99a1-c886f3ceb082",
        label: "Set geofencing rules & restrictions",
      },
      {
        id: "890688be-59ff-4cad-9964-56f7b3fc5ec8",
        label: "Control global discount and promo policies",
      },
      {
        id: "dca62c28-d697-422c-8bdc-9769cdd1f14b",
        label: "Configure ride cancellation policies",
      },
    ],
  },
  {
    title: "Reporting & Analytics",
    permissions: [
      {
        id: "f56f448d-cb9f-441d-819a-bc8b8d67110a",
        label:
          "View and export reports on revenue, ride activity, and system performance",
      },
      {
        id: "2e23460d-193d-40d5-9bae-3c2d52bae2da",
        label: "Monitor driver performance & customer ratings",
      },
      {
        id: "1ed43a60-f5cb-4466-b254-ce7a20128df9",
        label: "Analyze dispatcher efficiency",
      },
    ],
  },
];

type ClientResponse = {
  success: boolean;
  data: ClientType;
  meta: unknown;
};

const InputField = (props) => {
  const { hint, ...inputProps } = props;
  return (
    <div>
      <input
        {...inputProps}
        className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
      />

      {hint && (
        <p className="text-success-500 mt-1 text-xs font-bold">{hint}</p>
      )}
    </div>
  );
};

const SelectField = ({ children, ...props }) => (
  <select
    {...props}
    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
  >
    {children}
  </select>
);

// Reusable Accordion Wrapper
const AccordionItem = ({ title, children }) => (
  <div className="overflow-hidden rounded-sm">
    <button
      type="button"
      className="bg-tables flex w-full items-center justify-between px-4 py-3 text-left transition hover:bg-gray-200"
    >
      <h2 className="text-[14px] font-medium text-[#050013]">{title}</h2>
      <FaChevronDown
        className={`rotate-0 text-[#76787A] transition-transform duration-200`}
      />
    </button>
    {<div className="bg-white p-4">{children}</div>}
  </div>
);
interface CustomCheckboxProps {
  id: string;
  label: string;
  isChecked?: boolean;
  onChange?: (event?: React.ChangeEvent<HTMLInputElement>) => void;
  name?: string;
  isDisabled?: boolean;
}
// Reusable Custom Checkbox
const CustomCheckbox = ({
  id,
  label,
  isChecked,
  onChange,
  name,
  isDisabled = false,
}: CustomCheckboxProps) => (
  <label
    htmlFor={id}
    className={`flex items-start gap-3 text-[14px] transition ${
      isDisabled
        ? "cursor-not-allowed text-gray-400"
        : "cursor-pointer text-[#76787A] hover:text-[#050013]"
    }`}
  >
    <div className="relative mt-1 flex items-center">
      <input
        id={id}
        type="checkbox"
        className="peer hidden"
        checked={isChecked}
        onChange={!isDisabled ? (e) => onChange?.(e) : undefined}
        disabled={isDisabled}
        name={name}
      />
      <span
        className={`flex h-4 w-4 shrink-0 items-center justify-center rounded-sm border transition-colors ${
          isChecked && !isDisabled
            ? "border-[#3324E3] bg-[#3324E3]"
            : "border-gray-400"
        } ${isDisabled ? "bg-gray-200" : ""}`}
      >
        <FaCheck
          size={10}
          className={`text-white transition-opacity ${isChecked ? "opacity-100" : "opacity-0"}`}
        />
      </span>
    </div>
    <span
      className={`peer-checked:text-[#050013] ${isDisabled ? "text-gray-400" : ""}`}
    >
      {label}
    </span>
  </label>
);

const FileUpload = ({ title, acceptedFormats, onFileSelect }) => (
  <div className="mt-1">
    <p className="pb-3 text-[14px] font-medium text-[#76787A]">{title}</p>
    <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-none">
      <div className="flex w-full items-center justify-center">
        <label className="flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 py-8 hover:bg-gray-50">
          <div className="flex flex-col items-center justify-center pt-5 pb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="44"
              height="32"
              fill="none"
            >
              <path
                fill="#76787A"
                d="M21.818 0c3.348 0 6.705 1.265 9.258 3.818 1.981 1.981 3.161 4.46 3.606 7.03 5.07.824 8.954 5.192 8.954 10.485C43.636 27.213 38.85 32 32.97 32H9.212A9.213 9.213 0 0 1 0 22.788c0-4.952 3.918-8.956 8.818-9.167-.142-3.528 1.07-7.115 3.758-9.803A13.02 13.02 0 0 1 21.818 0m0 1.94a11.08 11.08 0 0 0-7.879 3.257 11.13 11.13 0 0 0-3.181 9.227.97.97 0 0 1-.97 1.091h-.576a7.236 7.236 0 0 0-7.273 7.273 7.236 7.236 0 0 0 7.273 7.273H32.97a8.713 8.713 0 0 0 8.727-8.728 8.72 8.72 0 0 0-7.924-8.697.97.97 0 0 1-.879-.848c-.28-2.41-1.33-4.74-3.182-6.591a11.12 11.12 0 0 0-7.894-3.258m0 9.696c.26.005.513.116.652.243l5.333 4.848c.399.347.418 1.001.076 1.38-.342.377-1.003.403-1.379.06l-3.712-3.38v10.425a.97.97 0 1 1-1.94 0V14.788l-3.712 3.379c-.376.343-1.018.3-1.378-.06-.374-.375-.304-1.04.075-1.38l5.334-4.848c.213-.195.394-.243.651-.243"
              />
            </svg>
            <p className="text-dark-grey text-sm">
              <span className="px-3 text-[14px]">
                Click or drag file to this area to upload
              </span>
            </p>
          </div>
          <input className="hidden" type="file" onChange={onFileSelect} />
        </label>
      </div>
    </div>
    <p className="py-3 text-[13px] text-[#76787A]">{acceptedFormats}</p>
  </div>
);

// --- Main Component ---
export default function UpdateClientPage() {
  const { id } = useParams<{ id: string }>();

  const [formData, setFormData] = useState({
    // Company Info
    companyName: "",
    legalName: "",
    companyRegistrationNumber: "",
    taxIdentificationNumber: "",
    businessType: "",
    websiteUrl: "",
    shortDescription: "",

    // Contact Info
    primaryContactName: "",
    primaryContactDesignation: "",
    primaryContactEmail: "",

    // Business Address
    headOfficeAddress: "",
    city: "",
    stateProvince: "",
    country: "",
    postalCode: "",

    // Payment & Billing
    billingCycle: "",
    invoiceEmail: "",
    legalRepresentativeName: "",

    // Service Details
    serviceType: "",
    operatingCities: "",
    fleetSize: "",
    availableVehicleTypes: "",
    wheelchairService: false,
    childSeatService: false,

    // Licensing & Compliance
    insuranceCompany: "",
    insurancePolicyNumber: "",
    insuranceExpiryDate: "",
    permitExpiryDate: "",

    superAdminId: "",

    //file upload
    logo: null,
    contract: null,
    legalRepresentativeSignature: null,
    businessLicense: null,
    regulatoryCertificate: null,
  });

  const [checkedPermissions, setCheckedPermissions] = useState([]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleFileChange = (e, fileKey) => {
    const file = e.target.files?.[0];
    if (file) setFormData((prev) => ({ ...prev, [fileKey]: file }));
  };

  const handleCheckboxChange = (id) => {
    setCheckedPermissions((prev) =>
      prev.includes(id) ? prev.filter((pid) => pid !== id) : [...prev, id],
    );
  };

  async function upDateClient(data: any) {
    return await sendMultipart(`clients/${id}`, data, "PATCH");
  }

  const { mutate: upDateClientMutation, isPending } = useMutation({
    mutationFn: upDateClient,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      setCheckedPermissions([]);
      setFormData({
        // Company Info
        companyName: "",
        legalName: "",
        companyRegistrationNumber: "",
        taxIdentificationNumber: "",
        businessType: "",
        websiteUrl: "",
        shortDescription: "",

        // Contact Info
        primaryContactName: "",
        primaryContactDesignation: "",
        primaryContactEmail: "",

        // Business Address
        headOfficeAddress: "",
        city: "",
        stateProvince: "",
        country: "",
        postalCode: "",

        // Payment & Billing
        billingCycle: "",
        invoiceEmail: "",
        legalRepresentativeName: "",

        // Service Details
        serviceType: "",
        operatingCities: "",
        fleetSize: "",
        availableVehicleTypes: "",
        wheelchairService: false,
        childSeatService: false,

        // Licensing & Compliance
        insuranceCompany: "",
        insurancePolicyNumber: "",
        insuranceExpiryDate: "",
        permitExpiryDate: "",

        superAdminId: "",

        logo: null,
        contract: null,
        legalRepresentativeSignature: null,
        businessLicense: null,
        regulatoryCertificate: null,
      });

      toast.success("Client updated Successfully", {
        autoClose: 5000,
        position: "top-center",
      });
    },
    onError: (err) => {
      toast.error("Failed to update Client");
    },
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const submissionData = {
      ...formData,
      permissionIds: checkedPermissions.join(","),
    };

    if (submissionData.insuranceExpiryDate) {
      submissionData.insuranceExpiryDate = new Date(
        submissionData.insuranceExpiryDate,
      ).toISOString();
    }

    if (submissionData.permitExpiryDate) {
      submissionData.permitExpiryDate = new Date(
        submissionData.permitExpiryDate,
      ).toISOString();
    }

    upDateClientMutation(submissionData);
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const { id } = JSON.parse(localStorage.getItem("account"));

        if (id) {
          setFormData((prev) => ({
            ...prev,
            superAdminId: id.toString() as string,
          }));
        }
      } catch (err) {
        console.error("Failed to parse account:", err);
      }
    }
  }, []);

  const getClientById = () => {
    return backendApiClient.get(`clients/${id}`).json<ClientResponse>();
  };

  const {
    data: clientResponse,
    isLoading,
    error,
  } = useQuery<ClientResponse>({
    queryKey: ["client", id],
    queryFn: getClientById,
    enabled: !!id,
  });

  useEffect(() => {
    if (clientResponse?.data) {
      const clientData = clientResponse.data;
      const formatDateForInput = (dateString) => {
        if (!dateString) return "";
        try {
          return new Date(dateString).toISOString().split("T")[0];
        } catch {
          return "";
        }
      };

      const sanitizedData = Object.entries(clientData).reduce(
        (acc, [key, value]) => {
          acc[key] = value === null ? "" : value;
          return acc;
        },
        {} as Partial<ClientType>,
      );

      setFormData({
        ...sanitizedData,
        insuranceExpiryDate: formatDateForInput(clientData.insuranceExpiryDate),
        permitExpiryDate: formatDateForInput(clientData.permitExpiryDate),
      });

      setCheckedPermissions(clientData.permissions?.map((p) => p.id) || []);
    }
  }, [clientResponse]);

  if (isLoading) {
    return <div className="p-6 text-center">Loading client details...</div>;
  }

  if (error || !clientResponse?.data) {
    return (
      <div className="p-6 text-center text-red-500">
        Failed to load client details.
      </div>
    );
  }
  return (
    <form
      onSubmit={handleSubmit}
      className="mx-auto max-w-4xl overflow-hidden rounded-[15px] p-4 sm:p-6 md:p-8"
    >
      <div className="mb-6 flex items-center gap-3">
        <Image src="/images/gen-info.svg" alt="Icon" width={48} height={48} />
        <h1 className="text-center text-[20px] font-medium text-[#050013]">
          General Info
        </h1>
      </div>

      <div className="space-y-4">
        {/* --- Company Information --- */}
        <AccordionItem title="Company Information">
          <div className="space-y-4">
            <InputField
              name="companyName"
              value={formData.companyName}
              onChange={handleInputChange}
              placeholder="Company Name"
            />
            <InputField
              name="legalName"
              value={formData.legalName}
              onChange={handleInputChange}
              placeholder="Legal name(if different)"
            />
            <InputField
              name="companyRegistrationNumber"
              value={formData.companyRegistrationNumber}
              onChange={handleInputChange}
              placeholder="Company Registration Number"
            />
            <InputField
              name="taxIdentificationNumber"
              value={formData.taxIdentificationNumber}
              onChange={handleInputChange}
              placeholder="Tax Identification Number (TIN/VAT)"
            />
            <InputField
              name="businessType"
              value={formData.businessType}
              onChange={handleInputChange}
              placeholder="Enter Your Business Type"
              hint={
                "businessType must be one of the following values: corporation, llc, partnership, sole_proprietorship, other"
              }
            />

            <InputField
              name="websiteUrl"
              value={formData.websiteUrl}
              onChange={handleInputChange}
              type="url"
              placeholder="Website URL"
            />
            <FileUpload
              title="Upload a logo"
              acceptedFormats="Formats accepted are PNG & JPG"
              onFileSelect={(e) => handleFileChange(e, "logo")}
            />
            <textarea
              name="shortDescription"
              value={formData.shortDescription}
              onChange={handleInputChange}
              placeholder="Short Company Description"
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              rows={3}
            ></textarea>
          </div>
        </AccordionItem>

        {/* --- Contact Information --- */}
        <AccordionItem title="Contact Information">
          <div className="space-y-4">
            <InputField
              name="primaryContactName"
              value={formData.primaryContactName}
              onChange={handleInputChange}
              placeholder="Primary Contact Person Name"
            />

            <InputField
              name="primaryContactDesignation"
              value={formData.primaryContactDesignation}
              onChange={handleInputChange}
              placeholder="Primary Contact Person Designation"
            />

            <InputField
              name="primaryContactEmail"
              value={formData.primaryContactEmail}
              onChange={handleInputChange}
              type="email"
              placeholder="Email Address"
            />
          </div>
        </AccordionItem>

        {/* --- Business Address --- */}
        <AccordionItem title="Business Address">
          <div className="space-y-4">
            <InputField
              name="headOfficeAddress"
              value={formData.headOfficeAddress}
              onChange={handleInputChange}
              placeholder="Head Office Address"
            />
            <InputField
              name="city"
              value={formData.city}
              onChange={handleInputChange}
              placeholder="City"
            />
            <InputField
              name="stateProvince"
              value={formData.stateProvince}
              onChange={handleInputChange}
              placeholder="State/Province"
            />
            <InputField
              name="country"
              value={formData.country}
              onChange={handleInputChange}
              placeholder="Country"
            />
            <InputField
              name="postalCode"
              value={formData.postalCode}
              onChange={handleInputChange}
              placeholder="Postal Code"
              type="number"
            />
          </div>
        </AccordionItem>

        {/* --- Payment & Billing --- */}
        <AccordionItem title="Payment & Billing">
          <div className="space-y-4">
            <SelectField
              name="billingCycle"
              value={formData.billingCycle}
              onChange={handleInputChange}
            >
              <option value="" disabled>
                Billing Cycle
              </option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
            </SelectField>
            <InputField
              name="invoiceEmail"
              value={formData.invoiceEmail}
              onChange={handleInputChange}
              type="email"
              placeholder="Invoice Email for Billing"
            />
            <FileUpload
              title="Contract Upload"
              acceptedFormats="Formats accepted are PNG & JPG"
              onFileSelect={(e) => handleFileChange(e, "contract")}
            />
            <InputField
              name="legalRepresentativeName"
              value={formData.legalRepresentativeName}
              onChange={handleInputChange}
              placeholder="Legal Representative name"
            />
            <FileUpload
              title="Upload Legal Representative Signature"
              acceptedFormats="Formats accepted are png & jpg"
              onFileSelect={(e) =>
                handleFileChange(e, "legalRepresentativeSignature")
              }
            />
          </div>
        </AccordionItem>

        {/* --- Service Details --- */}
        <AccordionItem title="Service Details">
          <div className="space-y-4">
            <InputField
              name="serviceType"
              value={formData.serviceType}
              onChange={handleInputChange}
              placeholder="Types of Services"
              hint="serviceType must be one of the following values: taxi, limousine, shuttle, delivery, other"
            />
            <InputField
              name="operatingCities"
              value={formData.operatingCities}
              onChange={handleInputChange}
              placeholder="Operating Cities/Regions"
            />
            <InputField
              name="fleetSize"
              value={formData.fleetSize}
              onChange={handleInputChange}
              type="number"
              placeholder="Fleet Size (Total number of vehicles)"
            />
            <InputField
              name="availableVehicleTypes"
              value={formData.availableVehicleTypes}
              onChange={handleInputChange}
              placeholder="Available Vehicle Types"
              hint="availableVehicleTypes must be one of the following values: sedan, suv, van, motorcycle, truck, other"
            />
            <div>
              <p className="mb-2 text-[13px] font-medium text-gray-600">
                Special Services
              </p>
              <div className="flex gap-6">
                <CustomCheckbox
                  id="wheelchairService"
                  name="wheelchairService"
                  label="Wheelchair Accessible"
                  isChecked={formData.wheelchairService}
                  onChange={handleInputChange}
                />
                <CustomCheckbox
                  id="childSeatService"
                  name="childSeatService"
                  label="Child Seat Available"
                  isChecked={formData.childSeatService}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>
        </AccordionItem>

        {/* --- Licensing & Compliance --- */}
        <AccordionItem title="Licensing & Compliance">
          <div className="space-y-4">
            <FileUpload
              title="Business License"
              acceptedFormats="Formats accepted are PNG & JPG"
              onFileSelect={(e) => handleFileChange(e, "businessLicense")}
            />
            <p className="pt-2 text-[14px] font-medium text-gray-700">
              Insurance Details
            </p>
            <InputField
              name="insuranceCompany"
              value={formData.insuranceCompany}
              onChange={handleInputChange}
              placeholder="Company"
            />
            <InputField
              name="insurancePolicyNumber"
              value={formData.insurancePolicyNumber}
              onChange={handleInputChange}
              placeholder="Policy Number"
            />
            <InputField
              name="insuranceExpiryDate"
              value={formData.insuranceExpiryDate}
              onChange={handleInputChange}
              type="date"
            />
            <FileUpload
              title="Regulatory Certificates (if applicable)"
              acceptedFormats="Formats accepted are PNG & JPG"
              onFileSelect={(e) => handleFileChange(e, "regulatoryCertificate")}
            />
            <InputField
              name="permitExpiryDate"
              value={formData.permitExpiryDate}
              onChange={handleInputChange}
              type="date"
            />
          </div>
        </AccordionItem>
      </div>

      {/* --- Give Permissions --- */}
      <div className="mt-8 mb-6 flex items-center gap-3">
        <Image
          src="/images/give-permission.svg"
          alt="Icon"
          width={48}
          height={48}
        />
        <h1 className="text-center text-[20px] font-medium text-[#050013]">
          Give Permissions
        </h1>
      </div>

      <div className="space-y-2">
        {permissionData.map((section) => (
          <AccordionItem key={section.title} title={section.title}>
            <div className="space-y-3 p-2">
              {section.permissions.map((perm) => (
                <CustomCheckbox
                  key={perm.id}
                  id={perm.id}
                  label={perm.label}
                  isChecked={checkedPermissions.includes(perm.id)}
                  onChange={() => handleCheckboxChange(perm.id)}
                />
              ))}
            </div>
          </AccordionItem>
        ))}
      </div>

      {/* --- Submit Button --- */}
      <div className="mt-8 text-center sm:text-right">
        <button
          disabled={isPending}
          type="submit"
          className="rounded-full bg-[#3324E3] px-8 py-2.5 font-medium text-white shadow-md transition hover:bg-[#291ed3] focus:ring-2 focus:ring-[#3324E3] focus:ring-offset-2 focus:outline-none"
        >
          {isPending ? "Updating..." : " Update"}
        </button>
      </div>
    </form>
  );
}
